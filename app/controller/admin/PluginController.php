<?php

namespace app\controller\admin;

use app\controller\admin\Controller;
use app\lib\llm\plugin\OpenApi;
use app\model\Plugin;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Post;
use think\annotation\route\Resource;
use yunwuxin\auth\middleware\Authentication;

#[Resource('plugin')]
#[Middleware(Authentication::class)]
class PluginController extends Controller
{
    public function index()
    {
        $plugins = Plugin::order('sort asc,id desc')->paginate();
        return json($plugins);
    }

    public function save()
    {
        $data = $this->validate([
            'title'       => 'require',
            'description' => 'require',
            'type'        => 'require',
            'icon'        => 'require',
            'sort'        => '',
        ]);

        return Plugin::create($data);
    }

    public function update($id)
    {
        $plugin = Plugin::findOrFail($id);

        $data = $this->validate([
            'title'       => 'require',
            'description' => 'require',
            'type'        => 'require',
            'icon'        => 'require',
            'sort'        => '',
        ]);

        $plugin->save($data);
    }

    public function delete($id)
    {
        $plugin = Plugin::findOrFail($id);

        $plugin->delete();
    }

    #[Post('plugin/:id/config')]
    public function config($id)
    {
        $plugin = Plugin::findOrFail($id);

        switch ($plugin->type) {
            case 'OpenApi':
                $data = $this->validate([
                    'schema' => 'require',
                    'auth'   => '',
                ]);
                $plugin->save([
                    'config' => $data,
                ]);
                break;
            case 'ThinkApi':
                $data = $this->validate([
                    'api' => 'require',
                ]);
                $plugin->save([
                    'config' => $data,
                ]);
                break;
            case 'Vision':
            case 'Artist':
                $data = $this->validate([
                    'models' => 'require',
                ]);
                $plugin->save([
                    'config' => $data,
                ]);
                break;
        }
    }

    #[Post('plugin/:id/status')]
    public function status($id)
    {
        $plugin = Plugin::findOrFail($id);

        $data = $this->validate([
            'status' => '',
        ]);

        return $plugin->save($data);
    }

    #[Post('plugin/parse')]
    public function parse()
    {
        $data = $this->validate([
            'schema' => '',
        ]);

        $plugin = new OpenApi($data);

        return $plugin->getTools();
    }

    #[Get('plugin/types')]
    public function types()
    {
        $types = Plugin::types();

        return array_map(function ($type) {
            return ['label' => $type, 'value' => $type];
        }, $types);
    }
}
