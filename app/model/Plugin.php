<?php

namespace app\model;

use app\lib\Hashids;
use Exception;
use think\annotation\model\option\Append;
use think\annotation\model\option\Type;
use think\db\Query;
use think\helper\Arr;
use think\helper\Str;
use think\Model;

/**
 * Class app\model\Plugin
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $sort
 * @property int $status
 * @property string $config
 * @property string $description
 * @property string $icon
 * @property string $title
 * @property string $type
 * @property-read mixed $name
 * @property-read mixed $tools
 * @method static \think\db\Query list()
 */
#[Type('config', 'json')]
#[Append(['name', 'tools'])]
class Plugin extends Model
{
    protected function getToolsAttr()
    {
        try {
            $provider = new ("app\lib\llm\plugin\\{$this->type}")($this->config);

            return $provider->getTools();
        } catch (\Throwable) {
            return [];
        }
    }

    protected function getNameAttr()
    {
        return Hashids::encode($this->id);
    }

    protected function getTypeAttr($type)
    {
        return match ($type) {
            1, '1' => 'OpenApi',
            2, '2' => 'ThinkApi',
            default => $type,
        };
    }

    protected function getIconAttr($icon)
    {
        if (Str::startsWith($icon, 'http')) {
            return $icon;
        }

        return (string) url($icon)->domain(true);
    }

    public static function getByName($name)
    {
        $id = Hashids::decode($name);
        return self::where('status', 1)->findOrFail($id);
    }

    /**
     * @param $name
     * @return \app\lib\llm\Tool
     */
    public function getTool($name)
    {
        $tool = Arr::first($this->tools, function ($tool) use ($name) {
            return $tool->getName() == $name;
        });

        if ($tool) {
            return $tool;
        }
        throw new Exception("{$name} tool not found");
    }

    public function scopeList(Query $query)
    {
        $query->order('sort asc')
            ->where('status', 1)
            ->visible(['name', 'icon', 'title', 'description', 'tools']);
    }

    public static function types()
    {
        $types = [
            'OpenApi',
            config('cloud.enable') ? 'ThinkApi' : null,
            'Artist',
            'Version',
        ];

        return array_values(array_filter_null($types));
    }
}
