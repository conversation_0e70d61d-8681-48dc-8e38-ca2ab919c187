<?php

namespace app\lib\llm\plugin;

use app\lib\llm\contract\PluginInterface;
use app\lib\llm\Tool;
use app\lib\llm\tool\Args;
use app\lib\llm\tool\result\Image;
use app\lib\llm\tool\result\Json;
use Exception;
use rpc\contract\api\Api;
use think\facade\Cache;
use think\helper\Arr;
use think\helper\Str;

class ThinkApi implements PluginInterface
{
    protected $api;

    public function __construct($config)
    {
        $this->api = Arr::get($config, 'api', []);
    }

    public function getTools()
    {
        $tools = [];
        if (config('cloud.enable')) {
            foreach ($this->api as $api) {
                $tools[] = new class($api) extends Tool {

                    protected $uri;
                    protected $fee;
                    protected $resultType;

                    protected Api $gateway;

                    public function __construct(protected $config)
                    {
                        $this->uri        = $this->config['uri'];
                        $this->fee        = $this->config['fee'];
                        $this->resultType = $this->config['result_type'] ?? 'json';

                        $this->gateway = app(Api::class);

                        $info = $this->getInfo();

                        $this->name        = str_replace('/', '_', $this->uri);
                        $this->title       = Arr::get($info, 'title');
                        $this->description = Arr::get($info, 'desc');

                        $args = Arr::get($info, 'args');

                        if (!empty($args)) {
                            $parameters = [];
                            foreach ($args as $arg) {
                                $parameters[$arg['name']] = [
                                    'type'        => 'string',
                                    'description' => $arg['desc'],
                                    'required'    => $arg['required'],
                                ];
                            }
                            $this->parameters = $parameters;
                        }
                    }

                    protected function getInfo()
                    {
                        //随机有效期 防止同时失效
                        $expire = rand(3600, 7200) * 24;

                        return Cache::remember('think-api-info' . $this->uri, function () {
                            return $this->gateway->meta($this->uri);
                        }, $expire);
                    }

                    protected function run(Args $args)
                    {
                        $data = [];

                        foreach ($args as $name => $value) {
                            $data[Str::camel($name)] = $value;
                        }

                        $res = $this->gateway->run($this->uri, $data);

                        if (Arr::get($res, 'code') == 0) {
                            $data = Arr::get($res, 'data');

                            switch ($this->resultType) {
                                case 'image':
                                    $result = new Image(Arr::get($data, $this->config['field']));
                                    break;
                                default:
                                    $result = new Json($data);
                            }

                            return $result->setUsage($this->getFee());
                        }
                        throw new Exception(Arr::get($res, 'message', '请求失败'));
                    }
                };
            }
        }
        return $tools;
    }

}
