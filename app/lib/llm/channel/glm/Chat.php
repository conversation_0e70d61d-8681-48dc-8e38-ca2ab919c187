<?php

namespace app\lib\llm\channel\glm;

use app\lib\llm\contract\ChatInterface;
use Psr\Http\Message\StreamInterface;

class Chat extends Driver implements ChatInterface
{
    const REASONING_START = "<think>\n";
    const REASONING_END   = "\n</think>\n";

    public function completions($params)
    {
        $model = $this->options['checkpoint'] ?? '';

        $messages    = $params['messages'] ?? [];
        $tools       = $params['tools'] ?? [];
        $stream      = $params['stream'] ?? true;
        $temperature = $params['temperature'] ?? null;

        //glm的bug 不支持传递空参数
        $tools = array_map(function ($tool) {
            if (isset($tool['function']) && empty($tool['function']['parameters'])) {
                $tool['function']['parameters'] = [
                    'type'       => 'object',
                    'properties' => [],
                ];
            }
            return $tool;
        }, $tools);

        //限制$temperature的范围在区间(0,1)
        $temperature = $temperature === 0 ? 0.01 : $temperature;
        $temperature = $temperature === 1 ? 0.99 : $temperature;

        $json = [
            'model'           => $model,
            'messages'        => $messages,
            'stream'          => $stream,
            'tools'           => $tools,
            'temperature'     => $temperature,
            'max_tokens'      => $params['max_tokens'] ?? null,
            'response_format' => $params['response_format'] ?? null,
            'user_id'         => $params['user'] ?? $params['prompt_cache_key'] ?? null,
        ];

        $thinking = $this->getThinking($params);
        if ($thinking) {
            $json['thinking'] = [
                'type' => $thinking,
            ];
        }

        $res = $this->request('chat/completions', [
            'json'   => $json,
            'stream' => $stream,
        ]);

        $startLength = mb_strlen(self::REASONING_START);
        $endLength   = mb_strlen(self::REASONING_END);

        if ($res instanceof StreamInterface) {
            $call      = null;
            $reasoning = null;
            $content   = '';

            foreach ($this->getMessages($res) as $message) {
                $data = $message['data'];
                if ($data == '[DONE]') {
                    break;
                }

                $result = json_decode($data, true);

                $delta        = $result['choices'][0]['delta'];
                $finishReason = $result['choices'][0]['finish_reason'] ?? null;

                //检查是否为tools_call
                if (!empty($delta['tool_calls'])) {
                    $current = $delta['tool_calls'][0];
                    if ($current['type'] == 'function') {
                        if (!isset($current['index']) || $current['index'] < 0/* glm的bug 会返回一个-1的index */) {
                            $current['index'] = 0;
                        }
                        if ($call) {
                            if ($call['index'] != $current['index']) {
                                yield [
                                    'delta'         => [
                                        'role'       => 'assistant',
                                        'content'    => null,
                                        'tool_calls' => [$call],
                                    ],
                                    'finish_reason' => null,
                                ];
                                $call = $current;
                            } else {
                                $call['function']['arguments'] .= $current['function']['arguments'];
                            }
                        } else {
                            $call = $current;
                        }
                    } else {
                        yield [
                            'delta'         => [
                                'role'       => 'assistant',
                                'content'    => null,
                                'tool_calls' => [$current],
                            ],
                            'finish_reason' => null,
                        ];
                    }
                } else {
                    if ($call) {
                        yield [
                            'delta'         => [
                                'role'       => 'assistant',
                                'content'    => null,
                                'tool_calls' => [$call],
                            ],
                            'finish_reason' => null,
                        ];
                        $call = null;
                    }
                    if (strlen($delta['content'] ?? '') > 0) {
                        if (is_null($reasoning)) {
                            $content .= $delta['content'];
                            if (mb_strlen($content) >= $startLength) {
                                if (str_starts_with($content, self::REASONING_START)) {
                                    $reasoning = true;
                                    yield [
                                        'delta'         => [
                                            'role'      => 'assistant',
                                            'reasoning' => mb_substr($content, $startLength),
                                        ],
                                        'finish_reason' => null,
                                    ];
                                } else {
                                    $reasoning = false;
                                    yield [
                                        'delta'         => [
                                            'role'    => 'assistant',
                                            'content' => $content,
                                        ],
                                        'finish_reason' => null,
                                    ];
                                }
                                $content = '';
                            }
                        } elseif ($reasoning) {
                            $content .= $delta['content'];
                            if (mb_strlen($content) >= $endLength) {
                                if (str_contains($content, self::REASONING_END)) {
                                    $reasoning = false;

                                    yield [
                                        'delta'         => [
                                            'role'      => 'assistant',
                                            'reasoning' => mb_substr($content, 0, mb_strpos($content, self::REASONING_END)),
                                        ],
                                        'finish_reason' => null,
                                    ];
                                    yield [
                                        'delta'         => [
                                            'role'    => 'assistant',
                                            'content' => mb_substr($content, mb_strpos($content, self::REASONING_END) + $endLength),
                                        ],
                                        'finish_reason' => null,
                                    ];

                                    $content = '';
                                } else {
                                    $text    = mb_substr($content, 0, -$endLength);
                                    $content = mb_substr($content, -$endLength);

                                    yield [
                                        'delta'         => [
                                            'role'      => 'assistant',
                                            'reasoning' => $text,
                                        ],
                                        'finish_reason' => null,
                                    ];
                                }
                            }
                        } else {
                            yield [
                                'delta'         => $delta,
                                'finish_reason' => null,
                            ];
                        }
                    }
                    if (strlen($delta['reasoning_content'] ?? '') > 0) {
                        if (isset($delta['reasoning_content'])) {
                            $delta['reasoning'] = $delta['reasoning_content'];
                            unset($delta['reasoning_content']);
                        }
                        yield [
                            'delta'         => $delta,
                            'finish_reason' => null,
                        ];
                    }
                }
            }

            if (!empty($content)) {
                yield [
                    'delta'         => [
                        'role'    => 'assistant',
                        'content' => $content,
                    ],
                    'finish_reason' => null,
                ];
            }

            if ($call) {
                yield [
                    'delta'         => [
                        'role'       => 'assistant',
                        'content'    => null,
                        'tool_calls' => [$call],
                    ],
                    'finish_reason' => null,
                ];
                $call = null;
            }

            yield [
                'usage'         => $this->applyFactor($result['usage'] ?? null),
                'finish_reason' => $finishReason ?? 'stop',
            ];
        } else {
            $message      = $res['choices'][0]['message'];
            $finishReason = $res['choices'][0]['finish_reason'];
            $usage        = $res['usage'] ?? null;

            if (!empty($message['reasoning_content'])) {
                $message['reasoning'] = $message['reasoning_content'];
                unset($message['reasoning_content']);
            } elseif (str_starts_with($message['content'] ?? '', self::REASONING_START)) {
                $message['reasoning'] = substr($message['content'], $startLength, strpos($message['content'], self::REASONING_END) - $startLength);
                $message['content']   = substr($message['content'], strpos($message['content'], self::REASONING_END) + $endLength);
            }

            return [
                'message'       => $message,
                'finish_reason' => $finishReason,
                'usage'         => $this->applyFactor($usage),
            ];
        }
    }
}
