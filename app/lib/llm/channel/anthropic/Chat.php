<?php

namespace app\lib\llm\channel\anthropic;

use Psr\Http\Message\StreamInterface;
use think\helper\Arr;
use think\helper\Str;

class Chat extends Driver
{
    public function completions($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $messages    = $params['messages'] ?? [];
        $tools       = $params['tools'] ?? null;
        $stream      = $params['stream'] ?? true;
        $moderation  = $params['moderation'] ?? false;
        $maxTokens   = $params['max_tokens'] ?? 8192;
        $temperature = $params['temperature'] ?? null;

        $extra = 0;
        if ($moderation) {
            $extra = $this->moderation($messages);
        }

        $system      = null;
        $toolContent = null;
        $index       = 0;

        $messages = Arr::flatMap(function ($item) use (&$system, &$toolContent, &$index) {
            $role = Arr::get($item, 'role');

            if ($role != 'tool') {
                ++$index;
            }
            switch (true) {
                case $role == 'system':
                    $system = Arr::get($item, 'content');
                    return [];
                case $role == 'assistant' and !empty($item['tool_calls']):
                    $content = Arr::flatMap(function ($call) {
                        $function = Arr::get($call, 'function');
                        if (empty($function)) {
                            return [];
                        }
                        $tool = [
                            'type'  => 'tool_use',
                            'id'    => $call['id'],
                            'name'  => $function['name'],
                            'input' => (object) json_decode($function['arguments'] ?? ''),
                        ];
                        return [$tool];
                    }, $item['tool_calls']);

                    if (!empty($item['content'])) {
                        array_unshift($content, [
                            'type' => 'text',
                            'text' => $item['content'],
                        ]);
                    }

                    $item = [
                        'role'    => 'assistant',
                        'content' => $content,
                    ];
                    break;
                case $role == 'tool':
                    if (empty($toolContent[$index])) {
                        $toolContent[$index] = [
                            [
                                'type'        => 'tool_result',
                                'tool_use_id' => Arr::get($item, 'tool_call_id'),
                                'content'     => Arr::get($item, 'content'),
                            ],
                        ];
                        $item                = [
                            'role'    => 'user',
                            'content' => &$toolContent[$index],
                        ];
                    } else {
                        $toolContent[$index][] = [
                            'type'        => 'tool_result',
                            'tool_use_id' => Arr::get($item, 'tool_call_id'),
                            'content'     => Arr::get($item, 'content'),
                        ];
                        return [];
                    }
                    break;
                case $role == 'user':
                    $content = Arr::get($item, 'content', '');
                    if (is_array($content)) {
                        $item = [
                            'role'    => 'user',
                            'content' => array_map(function ($item) {
                                return match ($item['type']) {
                                    'image_url' => [
                                        'type'   => 'image',
                                        'source' => $this->getImageSource(Arr::get($item, 'image_url.url')),
                                    ],
                                    default => $item
                                };
                            }, $content),
                        ];
                    }
                    break;
            }
            return [$item];
        }, $messages);

        if (!empty($tools)) {
            $tools = Arr::flatMap(function ($tool) {
                if (Arr::get($tool, 'type') == 'function') {
                    $function = Arr::get($tool, 'function', []);
                    return [
                        [
                            'name'         => $function['name'],
                            'input_schema' => $function['parameters'] ?? [
                                    'type' => 'object',
                                ],
                            'description'  => $function['description'] ?? null,
                        ],
                    ];
                }
                return [];
            }, $tools);
        }

        if (!empty($params['prompt_cache_key'])) {
            // 为最后一条消息的content添加cache_control
            if (!empty($messages)) {
                $lastIndex   = count($messages) - 1;
                $lastMessage = &$messages[$lastIndex];

                if (isset($lastMessage['content'])) {
                    if (is_array($lastMessage['content'])) {
                        // 如果content是数组，为最后一个元素添加cache_control
                        $lastContentIndex = count($lastMessage['content']) - 1;
                        if ($lastContentIndex >= 0) {
                            $lastMessage['content'][$lastContentIndex]['cache_control'] = [
                                'type' => 'ephemeral',
                            ];
                        }
                    } else {
                        // 如果content是字符串，转换为数组格式并添加cache_control
                        $lastMessage['content'] = [
                            [
                                'type'          => 'text',
                                'text'          => $lastMessage['content'],
                                'cache_control' => [
                                    'type' => 'ephemeral',
                                ],
                            ],
                        ];
                    }
                }
            }
        }

        $json = [
            'model'       => $model,
            'system'      => $system,
            'messages'    => $messages,
            'stream'      => $stream,
            'tools'       => $tools,
            'temperature' => $temperature,
            'max_tokens'  => $maxTokens,
            'metadata'    => [
                'user_id' => $params['user'] ?? null,
            ],
        ];

        $thinking = $this->getThinking($params);

        if ($thinking) {
            if ($thinking == 'enabled') {
                $json['thinking']    = [
                    'budget_tokens' => 8192,
                    'type'          => 'enabled',
                ];
                $json['max_tokens']  *= 2;
                $json['temperature'] = 1;
            } else {
                $json['thinking'] = [
                    'type' => 'disabled',
                ];
            }
        }

        $res = $this->request('/v1/messages', [
            'json'   => array_filter_null($json),
            'stream' => $stream,
        ]);

        if ($res instanceof StreamInterface) {
            $call         = null;
            $finishReason = null;
            $blockType    = null;
            $usage        = [];

            foreach ($this->getMessages($res) as $message) {
                $data   = $message['data'];
                $result = json_decode($data, true);
                $type   = Arr::get($result, 'type');

                switch ($type) {
                    case 'message_start':
                        $inputTokens          = Arr::get($result, 'message.usage.input_tokens', 0);
                        $cacheReadInputTokens = Arr::get($result, 'message.usage.cache_read_input_tokens', 0) * 0.1;

                        $cacheCreation = Arr::get($result, 'message.usage.cache_creation');
                        if ($cacheCreation) {
                            $cacheCreationInputTokens = $cacheCreation['ephemeral_5m_input_tokens'] * 1.25 + $cacheCreation['ephemeral_1h_input_tokens'] * 1.25 + $cacheCreation['ephemeral_1d_input_tokens'] * 2;
                        } else {
                            $cacheCreationInputTokens = Arr::get($result, 'message.usage.cache_creation_input_tokens', 0) * 1.25;
                        }

                        $usage['prompt_tokens'] = $inputTokens + $cacheCreationInputTokens + $cacheReadInputTokens;
                        break;
                    case 'message_delta':
                        $usage['completion_tokens'] = Arr::get($result, 'usage.output_tokens', 0);
                        $finishReason               = Arr::get($result, 'delta.stop_reason');
                        break;
                    case 'message_stop':
                        $finishReason = $this->getFinishReason($finishReason);
                        yield [
                            'usage'         => $this->applyFactor($usage),
                            'finish_reason' => $finishReason,
                        ];
                        break;
                    case 'content_block_start':
                        $blockType = Arr::get($result, 'content_block.type');
                        if ($blockType == 'tool_use') {
                            $call = [
                                'id'       => Arr::get($result, 'content_block.id'),
                                'type'     => 'function',
                                'function' => [
                                    'name'      => Arr::get($result, 'content_block.name'),
                                    'arguments' => '',
                                ],
                            ];
                        }
                        break;
                    case 'content_block_delta':
                        switch ($blockType) {
                            case 'thinking':
                                if ($result['delta']['type'] == 'thinking_delta') {
                                    yield [
                                        'delta'         => [
                                            'role'      => 'assistant',
                                            'reasoning' => $result['delta']['thinking'],
                                        ],
                                        'finish_reason' => null,
                                    ];
                                }
                                break;
                            case 'text':
                                if ($result['delta']['type'] == 'text_delta') {
                                    yield [
                                        'delta'         => [
                                            'role'    => 'assistant',
                                            'content' => $result['delta']['text'],
                                        ],
                                        'finish_reason' => null,
                                    ];
                                }
                                break;
                            case 'tool_use':
                                if ($result['delta']['type'] == 'input_json_delta') {
                                    $call['function']['arguments'] .= $result['delta']['partial_json'];
                                }
                                break;
                        }

                        break;
                    case 'content_block_stop':
                        if ($blockType == 'tool_use') {
                            yield [
                                'delta'         => [
                                    'role'       => 'assistant',
                                    'content'    => null,
                                    'tool_calls' => [$call],
                                ],
                                'finish_reason' => null,
                            ];
                            $call = null;
                        }
                        $blockType = null;
                        break;
                }
            }
        } else {
            $content   = '';
            $reasoning = '';
            $toolCalls = [];

            foreach ($res['content'] as $item) {
                switch ($item['type']) {
                    case 'thinking':
                        $reasoning .= $item['thinking'];
                        break;
                    case 'text':
                        $content .= $item['text'];
                        break;
                    case 'tool_use':
                        $toolCalls[] = [
                            'id'       => $item['id'],
                            'type'     => 'function',
                            'function' => [
                                'name'      => $item['name'],
                                'arguments' => json_encode($item['input']),
                            ],
                        ];
                        break;
                }
            }

            $message = [
                'role'    => 'assistant',
                'content' => $content,
            ];

            if (!empty($reasoning)) {
                $message['reasoning'] = $reasoning;
            }

            if (!empty($toolCalls)) {
                $message['tool_calls'] = $toolCalls;
            }

            $finishReason = $this->getFinishReason($res['stop_reason']);
            $usage        = [
                'prompt_tokens'     => $res['usage']['input_tokens'],
                'completion_tokens' => $res['usage']['output_tokens'],
            ];

            return [
                'message'       => $message,
                'finish_reason' => $finishReason,
                'usage'         => $this->applyFactor($usage, $extra),
            ];
        }
    }

    protected function getImageSource($url)
    {
        switch (true) {
            case Str::startsWith($url, 'data:'):
                // 解析 data URL: data:image/jpeg;base64,xxxxx
                $parts = explode(',', $url, 2);
                if (count($parts) == 2) {
                    // 提取 media_type: 从 data: 后面到逗号前面的部分
                    $mediaTypePart = substr($parts[0], 5); // 去掉 "data:" 前缀
                    // 去掉 ;base64 后缀（如果存在）
                    $mediaType = str_replace(';base64', '', $mediaTypePart);

                    return [
                        'type'       => 'base64',
                        'media_type' => $mediaType,
                        'data'       => $parts[1],
                    ];
                }
                break;
            case Str::startsWith($url, 'http'):
                return [
                    'type' => 'url',
                    'url'  => $url,
                ];
        }

        return null;
    }

    protected function getFinishReason($text)
    {
        return match ($text) {
            'end_turn' => 'stop',
            'tool_use' => 'tool_calls',
            default => $text,
        };
    }
}
