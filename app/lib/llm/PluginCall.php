<?php

namespace app\lib\llm;

use app\lib\Llm;
use app\lib\llm\contract\ChatInterface;
use app\lib\llm\tool\result\Error;
use app\model\Plugin;
use think\helper\Arr;
use Throwable;

class PluginCall implements ChatInterface
{
    protected $tools = [];

    public function __construct(protected Llm $llm, protected ChatInterface $chat)
    {
    }

    public function completions($params)
    {
        if (!empty($params['tools'])) {
            $params['tools'] = Arr::flatMap(function ($item) {
                if ($item['type'] == 'plugin') {
                    try {
                        $pluginName = $item['plugin']['name'];
                        $plugin     = Plugin::getByName($pluginName);

                        $tool = $plugin->getTool($item['plugin']['tool']);
                        $args = $item['plugin']['args'] ?? [];

                        $name = "plugin-{$pluginName}-{$tool->getName()}";

                        $this->tools[$name] = [$pluginName, $tool, $args];

                        return [
                            [
                                'type'     => 'function',
                                'function' => [
                                    'name'        => $name,
                                    'description' => $tool->getLlmDescription(),
                                    'parameters'  => $tool->getLlmParameters(),
                                ],
                            ],
                        ];
                    } catch (Throwable) {
                        return [];
                    }
                }
                return [$item];
            }, $params['tools']);
        }

        if (empty($params['tools'])) {
            unset($params['tools']);
        }

        $params['messages'] = Arr::flatMap(function ($message) {
            $toolMessages = [];
            if ($message['role'] == 'assistant' && !empty($message['tool_calls'])) {
                $message['tool_calls'] = array_map(function ($call) use (&$toolMessages) {
                    if ($call['type'] == 'plugin') {
                        $call['type']     = 'function';
                        $call['function'] = [
                            'name'      => "plugin-{$call['plugin']['name']}-{$call['plugin']['tool']}",
                            'arguments' => $call['plugin']['arguments'],
                        ];

                        $toolMessages[] = [
                            'role'         => 'tool',
                            'tool_call_id' => $call['id'],
                            'content'      => $call['plugin']['response'],
                        ];
                        unset($call['plugin']);
                    }
                    return $call;
                }, $message['tool_calls']);
            }
            return [$message, ...$toolMessages];
        }, $params['messages']);

        $generator = $this->chat->completions($params);

        foreach ($generator as $item) {
            if (!empty($item['delta']['tool_calls'])) {
                $call = $item['delta']['tool_calls'][0];
                if ($call['type'] == 'function' && isset($this->tools[$call['function']['name']])) {
                    foreach ($this->invokeTool($call) as $res) {
                        $item['delta']['tool_calls'] = [$res];
                        yield $item;
                    }
                    continue;
                }
            }

            yield $item;
        }

        $return = $generator->getReturn();

        if (!empty($return['message']['tool_calls'])) {
            $return['message']['tool_calls'] = array_map(function ($call) {
                if ($call['type'] == 'function' && isset($this->tools[$call['function']['name']])) {
                    $newCall = [];
                    foreach ($this->invokeTool($call) as $res) {
                        unset($res['index']);
                        $newCall = Arr::mergeDeep($newCall, $res);
                    }
                    return $newCall;
                }
                return $call;
            }, $return['message']['tool_calls']);
        }

        return $return;
    }

    protected function invokeTool($call)
    {
        /** @var \app\lib\llm\Tool $tool */
        [$name, $tool, $args] = $this->tools[$call['function']['name']];

        yield [
            'index'  => $call['index'] ?? null,
            'id'     => $call['id'] ?? '',
            'type'   => 'plugin',
            'plugin' => [
                'name'      => $name,
                'tool'      => $tool->getName(),
                'title'     => $tool->getTitle(),
                'function'  => $call['function']['name'],
                'arguments' => $call['function']['arguments'],
            ],
        ];

        try {
            $arguments = json_decode($call['function']['arguments'], true);

            if (!is_array($arguments)) {
                $arguments = [];
            }

            /** @var \app\lib\llm\tool\Result $result */
            $result = $tool(array_merge($arguments, $args));

            //调用工具产生的计费
            $usage = $result->getUsage();

            $this->llm->consumeTokens('plugin', "{$name}-{$tool->getName()}", $usage);
        } catch (Throwable $e) {
            $result = new Error($e);
        }

        yield [
            'index'  => $call['index'] ?? null,
            'plugin' => [
                'content'  => $result->getContent(),
                'response' => $result->getResponse(),
                'error'    => $result instanceof Error,
                'usage'    => $usage ?? 0,
            ],
        ];
    }

}
