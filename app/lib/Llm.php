<?php

namespace app\lib;

use app\lib\llm\Audio;
use app\lib\llm\Chat;
use app\lib\llm\Image;
use app\lib\llm\Music;
use app\lib\llm\PluginCall;
use app\lib\llm\Text;
use app\lib\llm\Video;
use app\model\Model;
use rpc\contract\cloud\Ai;
use think\exception\ValidateException;

class Llm
{
    protected ?Ai $ai = null;

    /** @var callable|null */
    protected $meter = null;

    public function chat()
    {
        $chat = new Chat($this);

        return new PluginCall($this, $chat);
    }

    public function image()
    {
        return new Image($this);
    }

    public function video()
    {
        return new Video($this);
    }

    public function audio()
    {
        return new Audio($this);
    }

    public function text()
    {
        return new Text($this);
    }

    public function music()
    {
        return new Music($this);
    }

    public function setMeter(callable $meter)
    {
        $this->meter = $meter;
    }

    public function consumeTokens($type, $code, $usage)
    {
        if ($this->meter) {
            call_user_func($this->meter, $type, $code, $usage);
        }
    }

    public function getDriver($type, $code, $method)
    {
        if ($code instanceof Model) {
            $model = $code;
        } else {
            $model = Model::where('code', $code)->where('type', $type)->find();
            if (!$model) {
                throw new ValidateException("The model `{$code}` does not exist");
            }
        }

        $driver = $model->getDriver($method);

        $driver->setLlm($this);

        return function ($params) use ($method, $driver) {
            return call_user_func([$driver, $method], $params);
        };
    }
}
